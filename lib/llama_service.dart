import 'dart:ffi';
import 'dart:io';
import 'dart:async';
import 'package:ffi/ffi.dart';
import 'package:llama_cpp_dart/llama_cpp_dart_ffi.dart';

class LlamaService {
  late final llama_cpp lib;
  late final Pointer<llama_model> model;
  late final Pointer<llama_context> ctx;
  late final Pointer<llama_vocab> vocab;
  Pointer<llama_sampler>? sampler; // Make nullable to avoid double-free
  bool _isLoaded = false; // Track if model is loaded

  bool load(String modelPath, {int ngl = 99, int nCtx = 2048}) {
    try {
      print('🔧 Starting model loading process...');
      print('📁 Model path: $modelPath');

      // Check if model file exists
      final modelFile = File(modelPath);
      if (!modelFile.existsSync()) {
        print('❌ Model file does not exist: $modelPath');
        return false;
      }
      print('✅ Model file exists, size: ${modelFile.lengthSync()} bytes');

      print('📚 Loading llama_cpp library...');
      lib = llama_cpp(
        DynamicLibrary.open(
          Platform.isAndroid ? 'libllama.so' : './libllama.so',
        ),
      );
      print('✅ llama_cpp library loaded successfully');

      print('🚀 Initializing llama backend...');
      lib.llama_backend_init();
      print('✅ Backend initialized');

      print('⚙️ Setting up model parameters...');
      final modelParams = lib.llama_model_default_params();
      modelParams.n_gpu_layers = ngl;
      print('✅ Model parameters configured (GPU layers: $ngl)');

      print('📖 Loading model from file...');
      final modelPathPtr = modelPath.toNativeUtf8().cast<Char>();
      model = lib.llama_load_model_from_file(modelPathPtr, modelParams);
      malloc.free(modelPathPtr);

      if (model.address == 0) {
        print('❌ Failed to load model - model pointer is null');
        return false;
      }
      print('✅ Model loaded successfully');

      print('📝 Getting vocabulary...');
      vocab = lib.llama_model_get_vocab(model);
      print('✅ Vocabulary loaded');

      // Initialize context here with proper nCtx parameter
      print('🔧 Creating context...');
      var ctxParams = lib.llama_context_default_params();
      ctxParams.n_ctx = nCtx;
      ctx = lib.llama_new_context_with_model(model, ctxParams);

      if (ctx.address == 0) {
        print('❌ Failed to create context - context pointer is null');
        return false;
      }
      print('✅ Context created successfully (context size: $nCtx)');

      _isLoaded = true;
      print('🎉 Model loading completed successfully!');
      return true;
    } catch (e) {
      print('❌ Error loading model: $e');
      return false;
    }
  }

  String generate(String prompt, {int nPredict = 256}) {
    if (!_isLoaded) {
      return "❌ Model not loaded. Please load the model first.";
    }

    try {
      print('🤖 Starting text generation...');
      print('📝 Prompt length: ${prompt.length} characters');

      final promptPtr = prompt.toNativeUtf8().cast<Char>();

      // Get token count first
      print('🔢 Tokenizing prompt...');
      final nPrompt = lib.llama_tokenize(
        vocab,
        promptPtr,
        prompt.length,
        nullptr,
        0,
        true,
        true,
      );

      // Handle negative return value (indicates error/required buffer size)
      final tokenCount = nPrompt < 0 ? -nPrompt : nPrompt;
      print('✅ Tokenization complete, token count: $tokenCount');

      final tokens = malloc<llama_token>(tokenCount);
      lib.llama_tokenize(
        vocab,
        promptPtr,
        prompt.length,
        tokens,
        tokenCount,
        true,
        true,
      );
      malloc.free(promptPtr);

      // Create sampler for this generation
      print('🎲 Initializing sampler...');
      var sparams = lib.llama_sampler_chain_default_params();
      sampler = lib.llama_sampler_chain_init(sparams);
      lib.llama_sampler_chain_add(sampler!, lib.llama_sampler_init_greedy());
      print('✅ Sampler initialized');

      final buffer = StringBuffer();
      final tokenPtr = malloc<llama_token>();

      var batch = lib.llama_batch_get_one(tokens, tokenCount);
      print('🔄 Starting generation loop...');

      int nDecode = 0;
      for (int nPos = 0; nPos + batch.n_tokens < tokenCount + nPredict;) {
        if (lib.llama_decode(ctx, batch) != 0) {
          print('❌ Decode failed at position $nPos');
          buffer.write("❌ decode failed");
          break;
        }
        nPos += batch.n_tokens;

        final newTokenId = lib.llama_sampler_sample(sampler!, ctx, -1);
        if (lib.llama_token_is_eog(vocab, newTokenId)) {
          print('🏁 End of generation detected');
          break;
        }

        final pieceBuf = malloc<Char>(128);
        final len = lib.llama_token_to_piece(
          vocab,
          newTokenId,
          pieceBuf,
          128,
          0,
          true,
        );
        if (len > 0) {
          final piece = String.fromCharCodes(
            pieceBuf.cast<Uint8>().asTypedList(len),
          );
          buffer.write(piece);
        }
        malloc.free(pieceBuf);

        tokenPtr.value = newTokenId;
        batch = lib.llama_batch_get_one(tokenPtr, 1);
        nDecode++;
      }

      print('✅ Generation complete, decoded $nDecode tokens');

      // Free sampler created in this method
      if (sampler != null) {
        lib.llama_sampler_free(sampler!);
        sampler = null;
      }

      malloc.free(tokenPtr);
      malloc.free(tokens);

      final result = buffer.toString();
      print('📤 Generated response: ${result.length} characters');
      return result;
    } catch (e) {
      print('❌ Error during generation: $e');
      return "❌ Error during generation: $e";
    }
  }

  Stream<String> generateStream(String prompt, {int nPredict = 2048}) async* {
    if (!_isLoaded) {
      yield "❌ Model not loaded. Please load the model first.";
      return;
    }

    try {
      print('🤖 Starting streaming text generation...');
      print('📝 Prompt length: ${prompt.length} characters');

      final promptPtr = prompt.toNativeUtf8().cast<Char>();

      // Get token count first
      print('🔢 Tokenizing prompt...');
      final nPrompt = lib.llama_tokenize(
        vocab,
        promptPtr,
        prompt.length,
        nullptr,
        0,
        true,
        true,
      );

      // Handle negative return value (indicates error/required buffer size)
      final tokenCount = nPrompt < 0 ? -nPrompt : nPrompt;
      print('✅ Tokenization complete, token count: $tokenCount');

      final tokens = malloc<llama_token>(tokenCount);
      lib.llama_tokenize(
        vocab,
        promptPtr,
        prompt.length,
        tokens,
        tokenCount,
        true,
        true,
      );
      malloc.free(promptPtr);

      // Create sampler for this generation
      print('🎲 Initializing sampler...');
      var sparams = lib.llama_sampler_chain_default_params();
      sampler = lib.llama_sampler_chain_init(sparams);
      lib.llama_sampler_chain_add(sampler!, lib.llama_sampler_init_greedy());
      print('✅ Sampler initialized');

      final buffer = StringBuffer();
      final tokenPtr = malloc<llama_token>();

      var batch = lib.llama_batch_get_one(tokens, tokenCount);
      print('🔄 Starting streaming generation loop...');

      int nDecode = 0;
      for (int nPos = 0; nPos + batch.n_tokens < tokenCount + nPredict;) {
        if (lib.llama_decode(ctx, batch) != 0) {
          print('❌ Decode failed at position $nPos');
          yield "❌ decode failed";
          break;
        }
        nPos += batch.n_tokens;

        final newTokenId = lib.llama_sampler_sample(sampler!, ctx, -1);
        if (lib.llama_token_is_eog(vocab, newTokenId)) {
          print('🏁 End of generation detected');
          break;
        }

        final pieceBuf = malloc<Char>(128);
        final len = lib.llama_token_to_piece(
          vocab,
          newTokenId,
          pieceBuf,
          128,
          0,
          true,
        );
        if (len > 0) {
          final piece = String.fromCharCodes(
            pieceBuf.cast<Uint8>().asTypedList(len),
          );
          buffer.write(piece);
          // Yield the accumulated text so far
          yield buffer.toString();
        }
        malloc.free(pieceBuf);

        tokenPtr.value = newTokenId;
        batch = lib.llama_batch_get_one(tokenPtr, 1);
        nDecode++;

        // Add a small delay to make streaming visible and prevent UI blocking
        await Future.delayed(const Duration(milliseconds: 10));
      }

      print('✅ Streaming generation complete, decoded $nDecode tokens');

      // Free sampler created in this method
      if (sampler != null) {
        lib.llama_sampler_free(sampler!);
        sampler = null;
      }

      malloc.free(tokenPtr);
      malloc.free(tokens);

      final result = buffer.toString();
      print('📤 Generated response: ${result.length} characters');
    } catch (e) {
      print('❌ Error during streaming generation: $e');
      yield "❌ Error during generation: $e";
    }
  }

  void dispose() {
    try {
      print('🧹 Disposing LlamaService...');
      // Only free sampler if it exists
      if (sampler != null) {
        lib.llama_sampler_free(sampler!);
        sampler = null;
        print('✅ Sampler freed');
      }

      if (_isLoaded) {
        lib.llama_free(ctx);
        lib.llama_free_model(model);
        lib.llama_backend_free();
        _isLoaded = false;
        print('✅ Model and context freed');
      }
      print('✅ LlamaService disposed successfully');
    } catch (e) {
      print('❌ Error during disposal: $e');
    }
  }

  bool get isLoaded => _isLoaded;
}
