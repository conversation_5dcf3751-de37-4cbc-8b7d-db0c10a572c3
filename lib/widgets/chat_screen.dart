import 'package:assistant/llama_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final bool isGenerating;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.isGenerating = false,
  });
}

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final llamaService = LlamaService();
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<ChatMessage> _messages = [];
  bool _isLoading = true;
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _initModel();
  }

  Future<void> _initModel() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Copy model from assets to device storage
      String modelPath = await _copyModelFromAssets();

      final success = llamaService.load(modelPath);

      setState(() {
        _isLoading = false;
        if (success) {
          _messages.add(
            ChatMessage(
              text:
                  "✅ Model loaded successfully! You can now start chatting with the local LLM.",
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
        } else {
          _messages.add(
            ChatMessage(
              text:
                  "❌ Failed to load model. Please check the console for detailed error messages.",
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _messages.add(
          ChatMessage(
            text:
                "❌ Error initializing model: $e\n\nPlease check if the model file exists in assets/models/.",
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
      });
    }
  }

  Future<String> _copyModelFromAssets() async {
    const modelName = 'gemma-3-1b-it-q2_k.gguf';
    const assetPath = 'assets/models/$modelName';

    try {
      // Get the app documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final modelFile = File('${appDir.path}/$modelName');

      // Check if model already exists in documents directory
      if (await modelFile.exists()) {
        print('✅ Model found in documents directory: ${modelFile.path}');
        return modelFile.path;
      }

      // Copy model from assets to documents directory
      print('📁 Copying model from assets to documents directory...');
      final byteData = await rootBundle.load(assetPath);
      final bytes = byteData.buffer.asUint8List();

      await modelFile.writeAsBytes(bytes);
      print('✅ Model copied successfully to: ${modelFile.path}');

      return modelFile.path;
    } catch (e) {
      print('❌ Error copying model: $e');
      throw Exception('Failed to copy model from assets: $e');
    }
  }

  void _sendMessage([String? promptOverride]) async {
    final prompt = promptOverride ?? _textController.text.trim();
    if (prompt.isEmpty || _isLoading || _isGenerating || !llamaService.isLoaded)
      return;

    // Add user message
    setState(() {
      _messages.add(
        ChatMessage(text: prompt, isUser: true, timestamp: DateTime.now()),
      );
      _textController.clear();
      _isGenerating = true;
    });

    // Add assistant message placeholder
    setState(() {
      _messages.add(
        ChatMessage(
          text: "",
          isUser: false,
          timestamp: DateTime.now(),
          isGenerating: true,
        ),
      );
    });

    _scrollToBottom();

    try {
      final formattedPrompt =
          "<start_of_turn>user\n$prompt<end_of_turn>\n<start_of_turn>model\n";

      // Use streaming generation
      await for (final partialResponse in llamaService.generateStream(
        formattedPrompt,
      )) {
        setState(() {
          // Update the last message (assistant's response) with the streaming text
          if (_messages.isNotEmpty) {
            _messages[_messages.length - 1] = ChatMessage(
              text: partialResponse,
              isUser: false,
              timestamp: DateTime.now(),
              isGenerating: true,
            );
          }
        });
        _scrollToBottom();
      }

      setState(() {
        _isGenerating = false;
        // Final update to remove the generating flag
        if (_messages.isNotEmpty) {
          _messages[_messages.length - 1] = ChatMessage(
            text: _messages.last.text,
            isUser: false,
            timestamp: DateTime.now(),
            isGenerating: false,
          );
        }
      });

      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isGenerating = false;
        // Update the last message with error
        if (_messages.isNotEmpty) {
          _messages[_messages.length - 1] = ChatMessage(
            text: "❌ Error generating response: $e",
            isUser: false,
            timestamp: DateTime.now(),
            isGenerating: false,
          );
        }
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    llamaService.dispose();
    _textController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Local LLM Chat"),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 2,
      ),
      body: Column(
        children: [
          // Chat messages area
          Expanded(
            child: _isLoading
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text("Loading model..."),
                      ],
                    ),
                  )
                : _messages.isEmpty
                ? const Center(
                    child: Text(
                      "Start a conversation!",
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final message = _messages[index];
                      return _buildMessageBubble(message);
                    },
                  ),
          ),

          // Input area
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border(
                top: BorderSide(color: Colors.grey.shade300, width: 0.5),
              ),
            ),
            child: Row(
              children: [
                // Text input
                Expanded(
                  child: TextField(
                    controller: _textController,
                    decoration: InputDecoration(
                      hintText: "Type your message...",
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade100,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                    enabled:
                        !_isLoading && !_isGenerating && llamaService.isLoaded,
                  ),
                ),
                const SizedBox(width: 8),

                // Send button
                Container(
                  decoration: BoxDecoration(
                    color: _isLoading || _isGenerating || !llamaService.isLoaded
                        ? Colors.grey.shade300
                        : Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed:
                        (_isLoading || _isGenerating || !llamaService.isLoaded)
                        ? null
                        : () => _sendMessage(),
                    icon: _isGenerating
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Icon(Icons.send, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.isUser;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // Assistant avatar
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.smart_toy, size: 20, color: Colors.blue),
            ),
            const SizedBox(width: 8),
          ],

          // Message bubble
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(18),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (message.text.isNotEmpty || message.isGenerating) ...[
                    Text(
                      message.text.isEmpty && message.isGenerating
                          ? "Thinking..."
                          : message.text,
                      style: TextStyle(
                        color: isUser ? Colors.white : Colors.black87,
                        fontSize: 16,
                      ),
                    ),
                  ],
                  if (message.isGenerating) ...[
                    const SizedBox(height: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isUser ? Colors.white70 : Colors.grey.shade600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          "Generating...",
                          style: TextStyle(
                            color: isUser
                                ? Colors.white70
                                : Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),

          if (isUser) ...[
            const SizedBox(width: 8),
            // User avatar
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.person, size: 20, color: Colors.green),
            ),
          ],
        ],
      ),
    );
  }
}
