# Local LLM Chat Assistant

A Flutter mobile application that runs a local Large Language Model (LLM) for chat interactions without requiring an internet connection.

## Features

- **Local LLM Processing**: Uses llama.cpp to run Gemma 3 1B model locally
- **Offline Chat**: No internet connection required for AI responses
- **Cross-platform**: Works on Android devices
- **Real-time Generation**: Streams responses as they're generated
- **Error Handling**: Robust error handling and user feedback

## Technical Details

### Dependencies

- `llama_cpp_dart`: Dart bindings for llama.cpp
- `flutter_tts`: Text-to-speech functionality
- `speech_to_text`: Speech recognition
- `path_provider`: File system access
- `ffi`: Foreign Function Interface for native code

### Model

- **Model**: Gemma 3 1B (quantized Q2_K)
- **Size**: ~658MB
- **Location**: `assets/models/gemma-3-1b-it-q2_k.gguf`

## Setup

1. **Clone the repository**
2. **Install dependencies**: `flutter pub get`
3. **Ensure model file exists**: The Gemma model should be in `assets/models/`
4. **Run the app**: `flutter run`

## Recent Improvements

### Memory Management

- Fixed double-free issues with llama sampler
- Added proper null checks and error handling
- Improved resource cleanup in dispose methods

### Error Handling

- Added comprehensive try-catch blocks
- Better user feedback for errors
- Model loading status tracking

### UI/UX Improvements

- Better loading states and user feedback
- Improved model path detection
- Disabled controls when model not loaded
- Added proper orientation handling

### Code Quality

- Removed unnecessary `Future.delayed` calls
- Added proper async/await patterns
- Better separation of concerns

## Usage

1. Launch the app
2. Wait for model to load (shows "✅ Model loaded successfully!")
3. Type your question in the text field
4. Press "Send" or use the "Ask Flutter" button for a sample question
5. View the AI response in the chat area

## Troubleshooting

### Model Loading Issues

- Ensure the model file exists in `assets/models/`
- Check device has sufficient storage space
- Verify the model file is not corrupted

### Performance Issues

- The app requires significant memory for model loading
- First generation may be slower due to model initialization
- Consider using a smaller model for better performance

## License

This project is for educational purposes.
